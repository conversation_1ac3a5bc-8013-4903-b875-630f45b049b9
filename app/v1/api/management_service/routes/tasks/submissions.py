"""
Routes for task submissions.
"""

from fastapi import APIRout<PERSON>, Depends, HTTPException, BackgroundTasks
from typing import Dict, List, Any, Optional
from datetime import datetime
from bson import ObjectId

from app.shared.security import get_tenant_info
from app.shared.models.user import UserTenantDB
from app.shared.utils.logger import setup_new_logging
from app.shared.api_errors import <PERSON>rrorCode, APIError, raise_api_error
from app.shared.api_response import APIResponse, ResponseMetadata

from app.v1.api.management_service.storage import TaskManager
from app.v1.api.management_service.models.task import TaskSubmissionRequest, TaskItemSubmissionRequest
from app.v1.api.management_service.models.responses import (
    TaskItemSubmissionResponse, TaskSetSubmissionResponse,
    TaskItemSubmissionAPIResponse, TaskSetSubmissionAPIResponse
)
"""
Routes for task submissions.
"""


# Configure logging
loggers = setup_new_logging(__name__)

router = APIRouter()

@router.post("/task-set", response_model=TaskSetSubmissionAPIResponse)
async def submit_task_set(
    request: TaskSubmissionRequest,
    user_tenant: UserTenantDB = Depends(get_tenant_info)
) -> TaskSetSubmissionAPIResponse:
    """
    Submit answers for a task set.

    Returns:
        TaskSetSubmissionAPIResponse: Standardized API response with submission results

    Raises:
        HTTPException: If the task set is not found, user doesn't have permission, or other errors occur
    """
    user_id = str(user_tenant.user.id)
    set_id = request.set_id
    answers = request.answers

    try:
        loggers.info(f"Submitting answers for task set {set_id} from user {user_id}")

        task_manager = TaskManager(current_user=user_tenant)

        # Get the task set to verify it exists and belongs to the user
        task_set = await task_manager.get_task_set(
            task_set_id=set_id,
            fields_to_retrieve=["user_id"]
        )

        if "error" in task_set:
            loggers.error(f"Error getting task set: {task_set['error']}")
            raise_api_error(
                error_code=ErrorCode.TASK_SET_NOT_FOUND,
                message=task_set["error"],
                status_code=404
            )

        # Verify the task set belongs to the user
        if task_set.get("user_id") != user_id:
            loggers.error(f"Task set {set_id} does not belong to user {user_id}")
            raise_api_error(
                error_code=ErrorCode.INSUFFICIENT_PERMISSIONS,
                message="You do not have permission to submit answers for this task set",
                status_code=403
            )

        # Submit the answers
        result = await task_manager.submit_task_set(
            task_set_id=set_id,
            user_id=user_id,
            answers=answers,
            current_user=user_tenant
        )

        if "error" in result:
            loggers.error(f"Error submitting task set: {result['error']}")
            raise_api_error(
                error_code=ErrorCode.TASK_SUBMISSION_FAILED,
                message=result["error"],
                status_code=500
            )

        # Create response model
        response = TaskSetSubmissionResponse(
            submission_id=result["submission_id"],
            task_set_id=result["task_set_id"],
            scores=result["scores"],
            score=result["score"],
            total_score=result["total_score"],
            completed_at=datetime.now()
        )

        loggers.info(
            f"Submitted task set {set_id}, score: {response.score}/{response.total_score}"
        )

        # Return standardized API response
        return APIResponse.success_response(
            data=response,
            meta=ResponseMetadata(
                timestamp=datetime.now().isoformat()
            )
        )
    except APIError as e:
        # Convert APIError to standardized error response
        return APIResponse.from_api_error(
            error=e,
            meta=ResponseMetadata(
                timestamp=datetime.now().isoformat()
            )
        )
    except Exception as e:
        loggers.error(f"Error submitting task set: {str(e)}")
        return APIResponse.error_response(
            error_code=ErrorCode.INTERNAL_SERVER_ERROR,
            message=f"Error submitting task set: {str(e)}",
            meta=ResponseMetadata(
                timestamp=datetime.now().isoformat()
            )
        )

@router.post("/task-item", response_model=TaskItemSubmissionAPIResponse)
async def submit_task_item(
    request: TaskItemSubmissionRequest,
    background_tasks: BackgroundTasks,
    user_tenant: UserTenantDB = Depends(get_tenant_info)
) -> TaskItemSubmissionAPIResponse:
    """
    Submit an answer for a single task item.

    Returns:
        TaskItemSubmissionAPIResponse: Standardized API response with submission results

    Raises:
        HTTPException: If the task is not found, already completed, or other errors occur
    """
    user_id = str(user_tenant.user.id)

    try:
        loggers.info(f"Submitting answer for task {request.task_id} from user {user_tenant.user.id}")

        task_manager = TaskManager(current_user=user_tenant)

        try:
            # Submit the task item with selected option(s)
            result = await task_manager.submit_task_item(
                task_id=request.task_id,
                answer=request.answer,
                task_type=request.task_type,
                folder=request.folder,
            )

            if "error" in result:
                loggers.error(f"Error submitting task item: {result['error']}")
                raise_api_error(
                    error_code=ErrorCode.TASK_SUBMISSION_FAILED,
                    message=result["error"],
                    status_code=500
                )

            # Create response model
            response = TaskItemSubmissionResponse(
                task_id=request.task_id,
                is_correct=result["is_correct"],
                correct_answer=result["correct_answer"],
                scored=result["scored"],
                total_score=result["total_score"],
                feedback=result["feedback"],
                is_already_completed=False
            )

            loggers.info(
                f"Submitted answer for task {request.task_id}, result: {result['is_correct']}, score: {result['scored']}/{result['total_score']}"
            )



            # Return standardized API response
            return APIResponse.success_response(
                data=response,
                meta=ResponseMetadata(
                    timestamp=datetime.now().isoformat()
                )
            )

        except APIError as e:
            # Handle already completed tasks
            if e.error_code == ErrorCode.TASK_ALREADY_COMPLETED:
                # Create response for already completed task
                response = TaskItemSubmissionResponse(
                    task_id=request.task_id,
                    is_correct=False,
                    scored=0,
                    total_score=0,
                    feedback="This task has already been completed",
                    correct_answer={},
                    is_already_completed=True
                )

                return APIResponse.success_response(
                    data=response,
                    meta=ResponseMetadata(
                        timestamp=datetime.now().isoformat()
                    )
                )
            else:
                # Re-raise other API errors
                raise

    except APIError as e:
        # Convert APIError to standardized error response
        return APIResponse.from_api_error(
            error=e,
            meta=ResponseMetadata(
                timestamp=datetime.now().isoformat()
            )
        )
    
    except Exception as e:
        loggers.error(f"Error submitting task item: {str(e)}")
        return APIResponse.error_response(
            error_code=ErrorCode.INTERNAL_SERVER_ERROR,
            message=f"Error submitting task item: {str(e)}",
            meta=ResponseMetadata(
                timestamp=datetime.now().isoformat()
            )
        )

    finally:
        loggers.info(f"Finished processing submission for task {request.task_id} from user {user_id}")
        await _check_and_trigger_followup(request.task_id, user_tenant, background_tasks)

async def _check_and_trigger_followup(task_id: str, user_tenant: UserTenantDB, background_tasks: BackgroundTasks):
    """
    Check if all tasks in the task set are completed and trigger followup generation if needed.

    Args:
        task_id: The ID of the task that was just submitted
        user_tenant: The user tenant information
        background_tasks: FastAPI background tasks instance
    """
    try:
        # Get the task item to find its task set
        task_item = await user_tenant.async_db.task_items.find_one(
            {"_id": ObjectId(task_id)},
            {"task_set_id": 1}
        )

        if not task_item or not task_item.get("task_set_id"):
            loggers.warning(f"Could not find task set for task item {task_id}")
            return

        task_set_id = task_item["task_set_id"]

        # Get the task set to check completion status and type
        task_set = await user_tenant.async_db.task_sets.find_one(
            {"_id": ObjectId(task_set_id)},
            {"attempted_tasks": 1, "total_tasks": 1, "gentype": 1, "input_content": 1, "user_id": 1}
        )

        if not task_set:
            loggers.warning(f"Could not find task set {task_set_id}")
            return

        # Check if task set is now completed
        attempted_tasks = task_set.get("attempted_tasks", 0)
        total_tasks = task_set.get("total_tasks", 0)

        if attempted_tasks >= total_tasks and total_tasks > 0:
            # Check if this task set should generate followups (primary or existing followup)
            gen_type = task_set.get("gentype", "primary")
            if gen_type in ["primary", "follow_up"]:
                try:
                    # Import followup function to avoid circular imports
                    from app.v2.api.socket_service_v2.generator.folllowup import followup_generate

                    # Schedule followup generation as background task - only pass task_set_id and current_user
                    background_tasks.add_task(
                        followup_generate,
                        str(task_set_id),
                        user_tenant
                    )
                    loggers.info(f"Scheduled followup generation for completed task set {task_set_id} (type: {gen_type})")
                except Exception as e:
                    loggers.error(f"Error scheduling followup generation for task set {task_set_id}: {e}")
                    # Don't fail the task submission if followup scheduling fails
        else:
            loggers.debug(f"Task set {task_set_id} not yet completed: {attempted_tasks}/{total_tasks}")

    except Exception as e:
        loggers.error(f"Error checking followup generation for task {task_id}: {e}")
        # Don't fail the task submission if followup check fails

